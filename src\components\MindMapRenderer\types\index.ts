/**
 * TypeScript类型定义
 * 为SimpleMindMap组件提供完整的类型支持
 */

// 基础几何类型
export interface Point {
  x: number;
  y: number;
}

export interface Rectangle {
  left: number;
  top: number;
  right: number;
  bottom: number;
  width: number;
  height: number;
}

// SimpleMindMap实例类型
export interface SimpleMindMapInstance {
  // 核心方法
  setData: (data: any) => void;
  getData: (withConfig?: boolean) => any;
  updateData: (data: any) => void;
  destroy: () => void;

  // 事件系统
  on: (event: string, callback: Function) => void;
  off: (event: string, callback: Function) => void;
  emit: (event: string, ...args: any[]) => void;

  // 坐标转换
  toPos: (x: number, y: number) => Point;

  // 配置选项
  opt: {
    readonly?: boolean;
    isDisableDrag?: boolean;
    enableFreeDrag?: boolean;
    autoMoveWhenMouseInEdgeOnDrag?: boolean;
    dragOpacityConfig?: {
      beingDragNodeOpacity: number;
      cloneNodeOpacity: number;
    };
    dragPlaceholderRectFill?: string;
    dragPlaceholderLineConfig?: {
      color: string;
      width: number;
    };
    beforeDragStart?: (nodeList: any[]) => boolean | Promise<boolean>;
    beforeDragEnd?: (dragInfo: any) => boolean | Promise<boolean>;
    handleDragCloneNode?: (cloneNode: any) => void;
    [key: string]: any;
  };

  // 视图控制
  view: {
    fit: () => void;
    reset: () => void;
    getTransformData: () => any;
    setTransformData: (data: any) => void;
    getScale?: () => number;
    setScale?: (scale: number, cx?: number, cy?: number) => void;
    translateX: (step: number) => void;
    translateY: (step: number) => void;
  };

  // 渲染器
  renderer: {
    root?: any;
    activeNodeList: any[];
    clearActiveNodeList: () => void;
    addNodeToActiveList: (node: any) => void;
    activeMultiNode?: (nodeList: any[]) => void;
    findNodeByUid?: (uid: string) => any;
  };

  // 主题和配置
  setTheme?: (theme: string, notRender?: boolean) => void;
  setThemeConfig: (config: any, notRender?: boolean) => void;
  getTheme: () => string;
  getThemeConfig: (prop?: string) => any;
  resize: () => void;
  getElRectInfo: () => void;
}

// 节点类型
export interface MindMapNodeData {
  text: string;
  richText?: boolean;
  expand?: boolean;
  isActive?: boolean;
  uid?: string;
  icon?: any[];
  image?: string;
  imageTitle?: string;
  imageSize?: {
    width: number;
    height: number;
    custom?: boolean;
  };
  hyperlink?: string | { link: string; title?: string; };
  hyperlinkTitle?: string;
  note?: string;
  tag?: any[];
  generalization?: any[] | MindMapNode;
  // 样式相关
  fillColor?: string;
  color?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  borderDasharray?: string;
  fontFamily?: string;
  fontSize?: number;
  fontWeight?: string | number;
  lineHeight?: number;
  textDecoration?: string;
  shape?: string;
}

export interface MindMapNode {
  data: MindMapNodeData;
  children: MindMapNode[];
}

// 数据版本控制元数据
export interface MindMapDataMetadata {
  dataVersion: string;
  instanceId: string;
  timestamp: number;
}

// 增强版思维导图节点（包含版本控制）
export interface EnhancedMindMapNode extends MindMapNode {
  _metadata?: MindMapDataMetadata;
}

// 主题类型
export type MindMapTheme = 'dark' | 'light' | 'default' | string;

// 配置接口
export interface MindMapConfig {
  // 基础配置
  readonly: boolean;
  theme: string;
  layout: string;
  
  // 交互配置
  enableFreeDrag: boolean;
  isDisableDrag?: boolean;
  autoMoveWhenMouseInEdgeOnDrag?: boolean;
  dragOpacityConfig?: {
    beingDragNodeOpacity: number;
    cloneNodeOpacity: number;
  };
  dragPlaceholderRectFill?: string;
  dragPlaceholderLineConfig?: {
    color: string;
    width: number;
  };
  enableCtrlKeyNodeSelection: boolean;
  enableAutoEnterTextEditWhenKeydown: boolean;
  isEndNodeTextEditOnClickOuter: boolean;
  
  // 性能配置
  maxHistoryCount: number;
  maxNodeCacheCount: number;
  
  // 视图配置
  fit?: boolean; // 是否自动适应画布
  fitPadding: number;
  mouseScaleCenterUseMousePosition: boolean;

  // 滚动条插件配置
  isLimitMindMapInCanvasWhenHasScrollbar?: boolean;

  // 自定义配置
  createNodePrefixContent?: (node: any) => any;
  createNodePostfixContent?: (node: any) => any;
  beforeTextEdit?: (node: any) => boolean | Promise<boolean>;
  
  // 错误处理
  errorHandler?: (type: string, error: any) => void;
}

// 框选配置
export interface SelectionConfig {
  /** 长按检测时间 (ms) */
  longPressDelay: number;
  /** 鼠标移动阈值 (px) */
  moveThreshold: number;
  /** 选择框样式 */
  selectionBoxStyle: {
    strokeColor: string;
    strokeWidth: number;
    fillColor: string;
    fillOpacity: number;
  };
  /** 是否启用调试模式 */
  debug: boolean;
}

// 事件回调类型
export type NodeClickCallback = (node: any) => void;
export type DataChangeCallback = (data: MindMapNode) => void;
export type RenderCompleteCallback = () => void;
export type SelectionCompleteCallback = (nodes: any[]) => void;
export type ContextMenuShowCallback = (position: { x: number; y: number }, node: any) => void;
export type ContextMenuHideCallback = () => void;

// 事件管理器配置
export interface EventManagerConfig {
  onNodeClick?: NodeClickCallback;
  onDataChange?: DataChangeCallback;
  onRenderComplete?: RenderCompleteCallback;
  onContextMenuShow?: ContextMenuShowCallback;
  onContextMenuHide?: ContextMenuHideCallback;
}

// 样式管理器配置
export interface StyleConfig {
  backgroundColor: string;
  lineColor: string;
  lineWidth: number;
  root: {
    fillColor: string;
    color: string;
    borderColor: string;
    borderWidth: number;
    fontSize: number;
    fontFamily: string;
    fontWeight: string;
    marginX: number;
    marginY: number;
  };
  second: {
    fillColor: string;
    color: string;
    borderColor: string;
    borderWidth: number;
    fontSize: number;
    fontFamily: string;
    fontWeight: string;
    marginX: number;
    marginY: number;
  };
  node: {
    fillColor: string;
    color: string;
    borderColor: string;
    borderWidth: number;
    fontSize: number;
    fontFamily: string;
    fontWeight: string;
    marginX: number;
    marginY: number;
  };
  generalization: {
    fillColor: string;
    color: string;
    borderColor: string;
    borderWidth: number;
    fontSize: number;
    fontFamily: string;
    marginX: number;
    marginY: number;
  };
  generalizationLineColor: string;
  generalizationLineWidth: number;
  enableRichText: boolean;
  enableNodeCustomStyle: boolean;
}

// 默认配置
export const DEFAULT_SELECTION_CONFIG: SelectionConfig = {
  longPressDelay: 500,
  moveThreshold: 8,
  selectionBoxStyle: {
    strokeColor: '#8FBC8F',
    strokeWidth: 2,
    fillColor: '#8FBC8F',
    fillOpacity: 0.15
  },
  debug: false
};

export const DEFAULT_MINDMAP_CONFIG: Partial<MindMapConfig> = {
  readonly: false,
  // 移除默认主题设置，让StyleManager控制主题
  layout: 'logicalStructure',
  fit: false, // 禁用自动适应画布，保持用户视图位置
  enableFreeDrag: false, // 保持结构化拖拽
  isDisableDrag: false, // 确保拖拽功能启用
  enableCtrlKeyNodeSelection: true,
  enableAutoEnterTextEditWhenKeydown: true,
  isEndNodeTextEditOnClickOuter: true,
  maxHistoryCount: 100,
  maxNodeCacheCount: 200,
  fitPadding: 50,
  mouseScaleCenterUseMousePosition: true,
  // 拖拽相关配置
  autoMoveWhenMouseInEdgeOnDrag: false, // 禁用拖拽时自动移动画布，避免拖拽结束后继续移动
  dragOpacityConfig: {
    beingDragNodeOpacity: 0.7,
    cloneNodeOpacity: 0.8
  },
  dragPlaceholderRectFill: '#FFD700',
  dragPlaceholderLineConfig: {
    color: '#FFD700',
    width: 3
  },
  // 禁用滚动条插件相关功能，避免SVG rbox错误
  isLimitMindMapInCanvasWhenHasScrollbar: false,

  // 添加错误处理器，捕获SVG相关错误
  errorHandler: (type: string, error: any) => {
    if (error.message && error.message.includes('Getting rbox of element')) {
      console.warn('⚠️ SVG rbox错误已被捕获并忽略:', error.message);
      return; // 忽略这类错误
    }
    console.error(`❌ SimpleMindMap错误 [${type}]:`, error);
  }
};