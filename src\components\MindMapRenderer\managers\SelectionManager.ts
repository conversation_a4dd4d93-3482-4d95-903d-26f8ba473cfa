/**
 * SelectionManager - 框选管理器
 * 负责右键长按框选功能的实现
 * 
 * 职责：
 * - 右键长按检测
 * - 框选区域渲染
 * - 多节点选择逻辑
 * 
 * 设计原则：
 * - 简化状态：只有 idle 和 selecting 两个状态
 * - 官方API优先：使用官方的坐标转换和节点选择API
 */

import { SimpleMindMapInstance, Point, SelectionConfig, DEFAULT_SELECTION_CONFIG, SelectionCompleteCallback } from '../types';

export class SelectionManager {
  private mindMapInstance: SimpleMindMapInstance;
  private container: HTMLElement;
  private config: SelectionConfig;
  private onSelectionComplete?: SelectionCompleteCallback;

  // 简化状态：只有两种状态
  private isSelecting = false;
  
  // 选择过程数据
  private startPoint: Point | null = null;
  private currentPoint: Point | null = null;
  private longPressTimer: NodeJS.Timeout | null = null;
  
  // DOM元素
  private selectionBox: HTMLElement | null = null;
  
  // 事件处理器绑定
  private boundHandlers = {
    mousedown: this.handleMouseDown.bind(this),
    mousemove: this.handleMouseMove.bind(this),
    mouseup: this.handleMouseUp.bind(this),
    contextmenu: this.handleContextMenu.bind(this)
  };

  constructor(
    mindMapInstance: SimpleMindMapInstance,
    container: HTMLElement,
    onSelectionComplete?: SelectionCompleteCallback
  ) {
    this.mindMapInstance = mindMapInstance;
    this.container = container;
    this.config = DEFAULT_SELECTION_CONFIG;
    this.onSelectionComplete = onSelectionComplete;
  }

  /**
   * 初始化框选管理器
   */
  initialize(): void {
    this.createSelectionBox();
    this.bindEvents();
    console.log('✅ 框选管理器初始化完成');
  }

  /**
   * 创建选择框DOM元素
   */
  private createSelectionBox(): void {
    this.selectionBox = document.createElement('div');
    this.selectionBox.className = 'mindmap-selection-box';
    
    const { selectionBoxStyle } = this.config;
    this.selectionBox.style.cssText = `
      position: fixed;
      pointer-events: none;
      z-index: 10000;
      border: ${selectionBoxStyle.strokeWidth}px solid ${selectionBoxStyle.strokeColor};
      background-color: ${selectionBoxStyle.fillColor};
      opacity: ${selectionBoxStyle.fillOpacity};
      border-radius: 4px;
      display: none;
      box-sizing: border-box;
      box-shadow: 0 2px 8px rgba(143, 188, 143, 0.3);
    `;
    
    // 直接插入到body，避免容器层级问题
    document.body.appendChild(this.selectionBox);
  }

  /**
   * 绑定事件监听
   */
  private bindEvents(): void {
    // 不使用capture模式，让节点事件优先处理
    this.container.addEventListener('mousedown', this.boundHandlers.mousedown, { passive: false });
    this.container.addEventListener('contextmenu', this.boundHandlers.contextmenu, { passive: false });

    // 全局鼠标事件
    document.addEventListener('mousemove', this.boundHandlers.mousemove, { passive: false });
    document.addEventListener('mouseup', this.boundHandlers.mouseup, { passive: false });
  }

  /**
   * 解除事件监听
   */
  private unbindEvents(): void {
    this.container.removeEventListener('mousedown', this.boundHandlers.mousedown, true);
    this.container.removeEventListener('contextmenu', this.boundHandlers.contextmenu, true);
    
    document.removeEventListener('mousemove', this.boundHandlers.mousemove);
    document.removeEventListener('mouseup', this.boundHandlers.mouseup);
  }

  /**
   * 处理鼠标按下事件
   */
  private handleMouseDown(event: MouseEvent): void {
    // 只处理右键
    if (event.button !== 2) return;

    // 检查是否点击在节点上
    const target = event.target as HTMLElement;
    if (this.isClickOnNode(target)) {
      // 如果点击在节点上，不处理框选，让节点的右键菜单正常工作
      console.log('🖱️ 右键点击节点，跳过框选处理');
      return;
    }

    // 阻止默认右键菜单
    event.preventDefault();

    // 转换为画布坐标
    const canvasPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);
    this.startPoint = canvasPoint;

    // 设置长按检测定时器
    this.longPressTimer = setTimeout(() => {
      this.startSelection();
    }, this.config.longPressDelay);

    console.log('🖱️ 右键按下空白区域，开始检测长按');
  }

  /**
   * 处理鼠标移动事件
   */
  private handleMouseMove(event: MouseEvent): void {
    if (!this.startPoint) return;
    
    const currentPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);
    this.currentPoint = currentPoint;
    
    // 如果还在检测阶段，检查移动距离
    if (!this.isSelecting && this.longPressTimer) {
      const distance = this.calculateDistance(this.startPoint, currentPoint);
      if (distance > this.config.moveThreshold) {
        // 移动距离超过阈值，立即开始框选
        this.clearLongPressTimer();
        this.startSelection();
      }
    }
    
    // 如果正在框选，更新选择框
    if (this.isSelecting) {
      this.updateSelectionBox();
    }
  }

  /**
   * 处理鼠标释放事件
   */
  private handleMouseUp(event: MouseEvent): void {
    if (event.button !== 2) return;
    
    this.clearLongPressTimer();
    
    if (this.isSelecting) {
      this.completeSelection();
    } else {
      // 短按，重置状态
      this.resetSelection();
    }
  }

  /**
   * 处理右键菜单事件
   */
  private handleContextMenu(event: MouseEvent): void {
    // 只有在正在框选时才阻止右键菜单
    if (this.isSelecting) {
      event.stopPropagation();
      event.preventDefault();
      console.log('🚫 框选状态中，阻止右键菜单');
    } else {
      // 检查是否点击在节点上
      const target = event.target as HTMLElement;
      if (!this.isClickOnNode(target)) {
        // 如果不是点击节点，阻止默认右键菜单
        event.preventDefault();
        console.log('🚫 空白区域右键，阻止默认菜单');
      }
    }
  }

  /**
   * 开始框选
   */
  private startSelection(): void {
    this.isSelecting = true;
    this.showSelectionBox();
    console.log('📦 开始框选模式');
  }

  /**
   * 显示选择框
   */
  private showSelectionBox(): void {
    if (this.selectionBox) {
      this.selectionBox.style.display = 'block';
      this.updateSelectionBox();
    }
  }

  /**
   * 更新选择框位置和大小
   */
  private updateSelectionBox(): void {
    if (!this.selectionBox || !this.startPoint || !this.currentPoint) return;
    
    // 转换为屏幕坐标
    const containerRect = this.container.getBoundingClientRect();
    const startScreen = {
      x: this.startPoint.x + containerRect.left,
      y: this.startPoint.y + containerRect.top
    };
    const currentScreen = {
      x: this.currentPoint.x + containerRect.left,
      y: this.currentPoint.y + containerRect.top
    };
    
    const left = Math.min(startScreen.x, currentScreen.x);
    const top = Math.min(startScreen.y, currentScreen.y);
    const width = Math.abs(currentScreen.x - startScreen.x);
    const height = Math.abs(currentScreen.y - startScreen.y);
    
    this.selectionBox.style.left = `${left}px`;
    this.selectionBox.style.top = `${top}px`;
    this.selectionBox.style.width = `${width}px`;
    this.selectionBox.style.height = `${height}px`;
  }

  /**
   * 完成框选
   */
  private completeSelection(): void {
    if (!this.startPoint || !this.currentPoint) {
      this.resetSelection();
      return;
    }
    
    // 查找选择范围内的节点
    const selectedNodes = this.findNodesInSelection();
    
    if (selectedNodes.length > 0) {
      // 使用官方API激活节点
      this.activateNodes(selectedNodes);
      
      // 回调通知
      this.onSelectionComplete?.(selectedNodes);
      
      console.log(`✅ 框选完成，选中 ${selectedNodes.length} 个节点`);
    } else {
      console.log('ℹ️ 框选区域内没有节点');
    }
    
    // 延迟隐藏选择框，提供视觉反馈
    setTimeout(() => {
      this.resetSelection();
    }, 150);
  }

  /**
   * 查找选择范围内的节点
   */
  private findNodesInSelection(): any[] {
    if (!this.startPoint || !this.currentPoint || !this.mindMapInstance.renderer?.root) {
      return [];
    }
    
    const selectedNodes: any[] = [];
    const selectionRect = this.getSelectionRect();
    
    // 递归遍历节点树
    this.traverseNodes(this.mindMapInstance.renderer.root, (node: any) => {
      if (this.isNodeInSelection(node, selectionRect)) {
        selectedNodes.push(node);
      }
    });
    
    return selectedNodes;
  }

  /**
   * 递归遍历节点
   */
  private traverseNodes(node: any, callback: (node: any) => void): void {
    if (!node) return;
    
    callback(node);
    
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child: any) => this.traverseNodes(child, callback));
    }
  }

  /**
   * 检查节点是否在选择范围内
   */
  private isNodeInSelection(node: any, selectionRect: { left: number, top: number, right: number, bottom: number }): boolean {
    try {
      // 使用官方API获取节点位置
      const nodeRect = node.getRectInSvg?.();
      if (!nodeRect) return false;
      
      const { left, top, width, height } = nodeRect;
      const nodeRight = left + width;
      const nodeBottom = top + height;
      
      // 矩形相交检测
      return !(
        nodeRight < selectionRect.left || 
        left > selectionRect.right || 
        nodeBottom < selectionRect.top || 
        top > selectionRect.bottom
      );
    } catch (error) {
      console.warn('❌ 节点位置检测失败:', error);
      return false;
    }
  }

  /**
   * 获取选择矩形
   */
  private getSelectionRect(): { left: number, top: number, right: number, bottom: number } {
    if (!this.startPoint || !this.currentPoint) {
      return { left: 0, top: 0, right: 0, bottom: 0 };
    }
    
    const left = Math.min(this.startPoint.x, this.currentPoint.x);
    const top = Math.min(this.startPoint.y, this.currentPoint.y);
    const right = Math.max(this.startPoint.x, this.currentPoint.x);
    const bottom = Math.max(this.startPoint.y, this.currentPoint.y);
    
    return { left, top, right, bottom };
  }

  /**
   * 激活选中的节点
   */
  private activateNodes(nodes: any[]): void {
    try {
      // 使用官方API激活多个节点
      if (this.mindMapInstance.renderer?.activeMultiNode) {
        this.mindMapInstance.renderer.activeMultiNode(nodes);
      } else {
        // 降级方案
        this.mindMapInstance.renderer?.clearActiveNodeList();
        nodes.forEach(node => {
          this.mindMapInstance.renderer?.addNodeToActiveList(node);
        });
      }
    } catch (error) {
      console.error('❌ 激活节点失败:', error);
    }
  }

  /**
   * 检查是否点击在节点上
   */
  private isClickOnNode(target: HTMLElement): boolean {
    // 检查点击目标是否是节点或节点的子元素
    // SimpleMindMap的节点通常有特定的类名或属性
    let element = target;
    while (element && element !== this.container) {
      // 检查是否是SVG节点元素
      if (element.tagName === 'g' && element.getAttribute('data-nodeid')) {
        return true;
      }
      // 检查是否有节点相关的类名
      if (element.classList && (
        element.classList.contains('smm-node') ||
        element.classList.contains('node') ||
        element.getAttribute('data-node') !== null
      )) {
        return true;
      }
      element = element.parentElement as HTMLElement;
    }
    return false;
  }

  /**
   * 计算两点间距离
   */
  private calculateDistance(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }

  /**
   * 清除长按定时器
   */
  private clearLongPressTimer(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 重置选择状态
   */
  private resetSelection(): void {
    this.isSelecting = false;
    this.startPoint = null;
    this.currentPoint = null;
    this.clearLongPressTimer();
    
    if (this.selectionBox) {
      this.selectionBox.style.display = 'none';
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<SelectionConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 检查是否正在框选
   */
  isActive(): boolean {
    return this.isSelecting;
  }

  /**
   * 销毁框选管理器
   */
  destroy(): void {
    this.resetSelection();
    this.unbindEvents();
    
    if (this.selectionBox) {
      this.selectionBox.remove();
      this.selectionBox = null;
    }
    
    console.log('✅ 框选管理器销毁完成');
  }
}