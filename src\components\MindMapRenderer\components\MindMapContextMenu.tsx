/**
 * 思维导图专用右键菜单组件
 * 基于现有ContextMenu组件，提供思维导图节点操作功能
 * 符合宫崎骏手绘风格设计
 */

'use client'

import React, { useEffect, useRef, useMemo } from 'react'
import { ClipboardManager } from '../managers/ClipboardManager'

// 思维导图菜单项接口
export interface MindMapMenuItem {
  id: string
  label: string
  icon?: React.ReactNode
  action: (node: any, mindMapInstance: any) => void
  disabled?: (node: any) => boolean
  danger?: boolean
  separator?: boolean
}

// 思维导图右键菜单属性接口
export interface MindMapContextMenuProps {
  /** 当前右键的节点 */
  node: any
  /** 菜单显示位置 */
  position: { x: number; y: number }
  /** 思维导图实例 */
  mindMapInstance: any
  /** 剪贴板管理器 */
  clipboardManager: ClipboardManager
  /** 菜单关闭回调 */
  onClose: () => void
  /** 自定义样式类名 */
  className?: string
}

/**
 * 生成思维导图菜单项
 */
const generateMindMapMenuItems = (node: any, mindMapInstance: any, clipboardManager: ClipboardManager): MindMapMenuItem[] => {
  const isRootNode = node?.isRoot || false
  const hasChildren = node?.children && node.children.length > 0
  const isExpanded = node?.isExpand !== false
  const hasCopiedData = clipboardManager.hasCopiedData()

  return [
    // 编辑节点
    {
      id: 'edit',
      label: '编辑节点',
      action: (node, mindMap) => {
        try {
          // 首先激活节点
          mindMap.renderer.clearActiveNodeList()
          mindMap.renderer.addNodeToActiveList(node)

          // 进入文本编辑模式
          if (mindMap?.renderer?.textEdit) {
            mindMap.renderer.textEdit.editNode(node)
          }
          console.log('✅ 开始编辑节点:', node.getData())
        } catch (error) {
          console.error('❌ 编辑节点失败:', error)
        }
      },
      disabled: () => false
    },
    
    // 分隔线
    {
      id: 'separator-1',
      label: '',
      action: () => {},
      separator: true
    },
    
    // 添加子节点
    {
      id: 'add-child',
      label: '添加子节点',
      action: (node, mindMap) => {
        try {
          // 首先激活当前节点
          mindMap.renderer.clearActiveNodeList()
          mindMap.renderer.addNodeToActiveList(node)

          // 执行添加子节点命令
          mindMap.execCommand('INSERT_CHILD_NODE', true) // true表示自动进入编辑模式
          console.log('✅ 添加子节点成功')
        } catch (error) {
          console.error('❌ 添加子节点失败:', error)
        }
      },
      disabled: () => false
    },
    
    // 添加同级节点
    {
      id: 'add-sibling',
      label: '添加同级节点',
      action: (node, mindMap) => {
        try {
          // 根节点不能添加同级节点
          if (node?.isRoot) {
            console.warn('⚠️ 根节点不能添加同级节点')
            return
          }

          // 首先激活当前节点
          mindMap.renderer.clearActiveNodeList()
          mindMap.renderer.addNodeToActiveList(node)

          // 执行添加同级节点命令
          mindMap.execCommand('INSERT_NODE', true) // true表示自动进入编辑模式
          console.log('✅ 添加同级节点成功')
        } catch (error) {
          console.error('❌ 添加同级节点失败:', error)
        }
      },
      disabled: (node) => node?.isRoot || false
    },
    
    // 分隔线
    {
      id: 'separator-2',
      label: '',
      action: () => {},
      separator: true
    },
    
    // 复制节点
    {
      id: 'copy',
      label: '复制节点',
      action: (node, mindMap) => {
        try {
          // 使用剪贴板管理器复制节点
          const success = clipboardManager.copyNode(node)
          if (success) {
            console.log('✅ 复制节点成功:', node.getData())
          }
        } catch (error) {
          console.error('❌ 复制节点失败:', error)
        }
      },
      disabled: () => false
    },
    
    // 粘贴为子节点
    {
      id: 'paste-child',
      label: hasCopiedData ? `粘贴为子节点 (${clipboardManager.getCopiedDataSummary()})` : '粘贴为子节点',
      action: (node, mindMap) => {
        try {
          const success = clipboardManager.pasteAsChild(node)
          if (success) {
            console.log('✅ 粘贴为子节点成功')
          }
        } catch (error) {
          console.error('❌ 粘贴为子节点失败:', error)
        }
      },
      disabled: () => !hasCopiedData
    },

    // 粘贴为同级节点
    {
      id: 'paste-sibling',
      label: hasCopiedData ? `粘贴为同级节点 (${clipboardManager.getCopiedDataSummary()})` : '粘贴为同级节点',
      action: (node, mindMap) => {
        try {
          const success = clipboardManager.pasteAsSibling(node)
          if (success) {
            console.log('✅ 粘贴为同级节点成功')
          }
        } catch (error) {
          console.error('❌ 粘贴为同级节点失败:', error)
        }
      },
      disabled: (node) => !hasCopiedData || node?.isRoot || false
    },
    
    // 分隔线
    {
      id: 'separator-3',
      label: '',
      action: () => {},
      separator: true
    },
    
    // 展开/折叠节点
    {
      id: 'toggle-expand',
      label: hasChildren ? (isExpanded ? '折叠节点' : '展开节点') : '展开节点',
      action: (node, mindMap) => {
        try {
          if (hasChildren) {
            // 使用官方API设置节点展开状态
            mindMap.execCommand('SET_NODE_EXPAND', node, !isExpanded)
            console.log(`✅ ${isExpanded ? '折叠' : '展开'}节点成功:`, node.getData())
          }
        } catch (error) {
          console.error('❌ 切换节点展开状态失败:', error)
        }
      },
      disabled: () => !hasChildren
    },
    
    // 居中显示
    {
      id: 'center',
      label: '居中显示',
      action: (node, mindMap) => {
        try {
          // 使用SimpleMindMap的官方API将节点移动到画布中心
          if (mindMap?.renderer?.moveNodeToCenter) {
            // 不重置缩放，保持当前缩放级别
            mindMap.renderer.moveNodeToCenter(node, false)
            console.log('✅ 节点居中显示成功:', node.getData())
          } else {
            // 降级方案：手动计算居中位置
            const nodeRect = node.getRect()
            const viewportWidth = mindMap.width
            const viewportHeight = mindMap.height

            // 计算节点中心点
            const nodeCenterX = nodeRect.x + nodeRect.width / 2
            const nodeCenterY = nodeRect.y + nodeRect.height / 2

            // 计算画布中心点
            const canvasCenterX = viewportWidth / 2
            const canvasCenterY = viewportHeight / 2

            // 计算需要移动的距离
            const offsetX = canvasCenterX - nodeCenterX
            const offsetY = canvasCenterY - nodeCenterY

            // 执行平移
            mindMap.view.translateXY(offsetX, offsetY)
            console.log('✅ 节点居中显示成功（降级方案）:', { offsetX, offsetY })
          }
        } catch (error) {
          console.error('❌ 节点居中显示失败:', error)
        }
      },
      disabled: () => false
    },
    
    // 分隔线
    {
      id: 'separator-4',
      label: '',
      action: () => {},
      separator: true
    },
    
    // 删除节点
    {
      id: 'delete',
      label: '删除节点',
      action: (node, mindMap) => {
        try {
          // 根节点不能删除
          if (node?.isRoot) {
            console.warn('⚠️ 根节点不能删除')
            return
          }

          // 首先激活当前节点
          mindMap.renderer.clearActiveNodeList()
          mindMap.renderer.addNodeToActiveList(node)

          // 执行删除节点命令
          mindMap.execCommand('REMOVE_NODE', [node])
          console.log('✅ 删除节点成功:', node.getData())
        } catch (error) {
          console.error('❌ 删除节点失败:', error)
        }
      },
      disabled: (node) => node?.isRoot || false,
      danger: true
    }
  ]
}

/**
 * 思维导图右键菜单组件
 */
export default function MindMapContextMenu({
  node,
  position,
  mindMapInstance,
  clipboardManager,
  onClose,
  className = ''
}: MindMapContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  // 生成菜单项
  const menuItems = useMemo(() => {
    return generateMindMapMenuItems(node, mindMapInstance, clipboardManager)
  }, [node, mindMapInstance, clipboardManager])

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    // 延迟添加事件监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }, 100)

    return () => {
      clearTimeout(timer)
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  // 调整菜单位置，确保不超出视窗
  const adjustPosition = () => {
    if (!menuRef.current) return position

    const rect = menuRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let { x, y } = position

    // 水平方向调整
    if (x + rect.width > viewportWidth) {
      x = viewportWidth - rect.width - 10
    }
    if (x < 10) {
      x = 10
    }

    // 垂直方向调整
    if (y + rect.height > viewportHeight) {
      y = viewportHeight - rect.height - 10
    }
    if (y < 10) {
      y = 10
    }

    return { x, y }
  }

  const adjustedPosition = adjustPosition()

  // 处理菜单项点击
  const handleMenuItemClick = (item: MindMapMenuItem) => {
    if (item.disabled?.(node)) return
    
    try {
      item.action(node, mindMapInstance)
      onClose() // 执行操作后关闭菜单
    } catch (error) {
      console.error('菜单操作执行失败:', error)
    }
  }

  return (
    <div
      ref={menuRef}
      className={`
        fixed z-50 min-w-40 py-1
        bg-primary-beige border-2 border-primary-green rounded-md
        shadow-lg animate-in fade-in-0 zoom-in-95 duration-200
        ${className}
      `}
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
        background: 'var(--color-primary-beige)',
        borderColor: 'var(--color-primary-green)',
        boxShadow: '0 4px 16px rgba(143, 188, 143, 0.3)',
        fontFamily: 'var(--font-family-handwritten)'
      }}
    >
      {menuItems.map((item, index) => {
        // 渲染分隔线
        if (item.separator) {
          return (
            <div
              key={item.id}
              className="h-px mx-2 my-1"
              style={{
                background: 'var(--color-sage-green)',
                opacity: 0.3
              }}
            />
          )
        }

        const isDisabled = item.disabled?.(node) || false

        return (
          <button
            key={item.id}
            className={`
              w-full px-3 py-1.5 text-left flex items-center gap-2
              transition-colors duration-150 text-sm
              ${item.danger
                ? 'text-red-600 hover:bg-red-50'
                : 'hover:bg-sage-green hover:bg-opacity-20'
              }
              ${isDisabled
                ? 'opacity-50 cursor-not-allowed'
                : 'cursor-pointer'
              }
            `}
            style={{
              color: item.danger 
                ? 'var(--color-error)' 
                : isDisabled 
                  ? 'var(--color-dark-gray)' 
                  : 'var(--color-black)',
              fontFamily: 'var(--font-family-handwritten)',
              fontSize: 'var(--font-size-sm)'
            }}
            onClick={() => handleMenuItemClick(item)}
            disabled={isDisabled}
          >
            {item.icon && (
              <span className="flex-shrink-0">
                {item.icon}
              </span>
            )}
            <span className="flex-1">
              {item.label}
            </span>
          </button>
        )
      })}
    </div>
  )
}
