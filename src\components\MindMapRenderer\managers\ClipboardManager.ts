/**
 * 剪贴板管理器
 * 负责思维导图节点的复制粘贴功能
 */

export class ClipboardManager {
  private copiedNodeData: any = null;
  private mindMapInstance: any = null;

  constructor(mindMapInstance: any) {
    this.mindMapInstance = mindMapInstance;
  }

  /**
   * 复制节点
   */
  copyNode(node: any): boolean {
    try {
      if (!node || !this.mindMapInstance) {
        console.warn('⚠️ 无效的节点或思维导图实例');
        return false;
      }

      // 激活目标节点
      this.mindMapInstance.renderer.clearActiveNodeList();
      this.mindMapInstance.renderer.addNodeToActiveList(node);

      // 使用SimpleMindMap的内部复制方法
      const copiedData = this.mindMapInstance.renderer.copyNode();
      
      if (copiedData && copiedData.length > 0) {
        this.copiedNodeData = copiedData;
        console.log('✅ 复制节点成功:', copiedData);
        return true;
      } else {
        console.warn('⚠️ 复制节点失败，没有获取到数据');
        return false;
      }
    } catch (error) {
      console.error('❌ 复制节点失败:', error);
      return false;
    }
  }

  /**
   * 粘贴节点作为子节点
   */
  pasteAsChild(targetNode: any): boolean {
    try {
      if (!this.hasCopiedData()) {
        console.warn('⚠️ 没有可粘贴的数据');
        return false;
      }

      if (!targetNode || !this.mindMapInstance) {
        console.warn('⚠️ 无效的目标节点或思维导图实例');
        return false;
      }

      // 激活目标节点
      this.mindMapInstance.renderer.clearActiveNodeList();
      this.mindMapInstance.renderer.addNodeToActiveList(targetNode);

      // 执行粘贴命令
      this.mindMapInstance.execCommand('PASTE_NODE', this.copiedNodeData);
      
      console.log('✅ 粘贴为子节点成功');
      return true;
    } catch (error) {
      console.error('❌ 粘贴为子节点失败:', error);
      return false;
    }
  }

  /**
   * 粘贴节点作为同级节点
   */
  pasteAsSibling(targetNode: any): boolean {
    try {
      if (!this.hasCopiedData()) {
        console.warn('⚠️ 没有可粘贴的数据');
        return false;
      }

      if (!targetNode || !this.mindMapInstance) {
        console.warn('⚠️ 无效的目标节点或思维导图实例');
        return false;
      }

      // 根节点不能粘贴同级节点
      if (targetNode.isRoot) {
        console.warn('⚠️ 根节点不能粘贴同级节点');
        return false;
      }

      // 激活目标节点的父节点
      if (targetNode.parent) {
        this.mindMapInstance.renderer.clearActiveNodeList();
        this.mindMapInstance.renderer.addNodeToActiveList(targetNode.parent);

        // 执行粘贴命令
        this.mindMapInstance.execCommand('PASTE_NODE', this.copiedNodeData);
        
        console.log('✅ 粘贴为同级节点成功');
        return true;
      } else {
        console.warn('⚠️ 目标节点没有父节点');
        return false;
      }
    } catch (error) {
      console.error('❌ 粘贴为同级节点失败:', error);
      return false;
    }
  }

  /**
   * 检查是否有可粘贴的数据
   */
  hasCopiedData(): boolean {
    return this.copiedNodeData !== null && Array.isArray(this.copiedNodeData) && this.copiedNodeData.length > 0;
  }

  /**
   * 获取复制的数据
   */
  getCopiedData(): any {
    return this.copiedNodeData;
  }

  /**
   * 清空复制的数据
   */
  clearCopiedData(): void {
    this.copiedNodeData = null;
    console.log('✅ 清空复制数据');
  }

  /**
   * 获取复制数据的摘要信息
   */
  getCopiedDataSummary(): string {
    if (!this.hasCopiedData()) {
      return '无复制数据';
    }

    const nodeCount = this.copiedNodeData.length;
    const firstNodeText = this.copiedNodeData[0]?.data?.text || '未知节点';
    
    if (nodeCount === 1) {
      return `已复制: ${firstNodeText}`;
    } else {
      return `已复制: ${firstNodeText} 等 ${nodeCount} 个节点`;
    }
  }

  /**
   * 销毁剪贴板管理器
   */
  destroy(): void {
    this.clearCopiedData();
    this.mindMapInstance = null;
    console.log('✅ 剪贴板管理器销毁完成');
  }
}
